import React from 'react';
import { CheckIcon, StarIcon } from '@heroicons/react/24/outline';
import { Button } from '@/components/ui/button';
import { SubscriptionTier } from '@/lib/stripe-client';

interface PlanCardProps {
  tier: SubscriptionTier;
  name: string;
  price: number;
  features: string[];
  isCurrentPlan: boolean;
  isPopular?: boolean;
  onSelectPlan: (tier: SubscriptionTier) => void;
  loading?: boolean;
}

export function PlanCard({
  tier,
  name,
  price,
  features,
  isCurrentPlan,
  isPopular = false,
  onSelectPlan,
  loading = false
}: PlanCardProps) {
  const handleSelectPlan = () => {
    if (!isCurrentPlan && !loading) {
      onSelectPlan(tier);
    }
  };

  return (
    <div className={`relative bg-gray-900/50 backdrop-blur-sm rounded-lg border p-6 transition-all duration-200 ${
      isCurrentPlan 
        ? 'border-green-500/50 ring-2 ring-green-500/20' 
        : 'border-gray-800/50 hover:border-gray-700/50 hover:shadow-lg'
    }`}>
      {/* Popular Badge */}
      {isPopular && !isCurrentPlan && (
        <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
          <div className="bg-gradient-to-r from-cyan-500 to-cyan-600 text-white px-3 py-1 rounded-full text-xs font-semibold flex items-center gap-1">
            <StarIcon className="h-3 w-3" />
            Most Popular
          </div>
        </div>
      )}

      {/* Current Plan Badge */}
      {isCurrentPlan && (
        <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
          <div className="bg-green-500 text-white px-3 py-1 rounded-full text-xs font-semibold flex items-center gap-1">
            <CheckIcon className="h-3 w-3" />
            Current Plan
          </div>
        </div>
      )}

      {/* Plan Header */}
      <div className="text-center mb-6">
        <h3 className="text-xl font-bold text-white mb-2">{name}</h3>
        <div className="text-3xl font-bold text-white">
          {price === 0 ? (
            <span className="text-green-400">Free</span>
          ) : (
            <>
              <span>${price}</span>
              <span className="text-gray-400 text-base font-normal">/month</span>
            </>
          )}
        </div>
      </div>

      {/* Features List */}
      <div className="space-y-3 mb-6">
        {features.map((feature, index) => (
          <div key={index} className="flex items-start gap-3">
            <CheckIcon className="h-4 w-4 text-green-400 mt-0.5 flex-shrink-0" />
            <span className="text-gray-300 text-sm">{feature}</span>
          </div>
        ))}
      </div>

      {/* Action Button */}
      <Button
        onClick={handleSelectPlan}
        disabled={isCurrentPlan || loading}
        className={`w-full transition-all duration-200 ${
          isCurrentPlan
            ? 'bg-green-500/20 text-green-400 border border-green-500/30 cursor-not-allowed'
            : 'bg-gradient-to-r from-cyan-500 to-cyan-600 hover:from-cyan-600 hover:to-cyan-700 text-white shadow-lg hover:shadow-xl'
        }`}
        size="lg"
      >
        {isCurrentPlan ? 'Current Plan' : `Select ${name}`}
      </Button>
    </div>
  );
}
