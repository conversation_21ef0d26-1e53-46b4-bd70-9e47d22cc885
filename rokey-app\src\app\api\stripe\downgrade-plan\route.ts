import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { getStripe } from '@/lib/stripe';
import { TIER_CONFIGS, SubscriptionTier } from '@/lib/stripe-client';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

const stripe = getStripe();

export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const { userId, newTier } = body;

    console.log('🔄 Downgrade request received');
    console.log('👤 User ID:', userId);
    console.log('📉 New tier:', newTier);

    if (!userId || !newTier) {
      console.error('❌ Missing userId or newTier in request');
      return NextResponse.json(
        { error: 'Missing userId or newTier' },
        { status: 400 }
      );
    }

    // Validate the new tier
    if (!TIER_CONFIGS[newTier as SubscriptionTier]) {
      return NextResponse.json(
        { error: 'Invalid tier' },
        { status: 400 }
      );
    }

    // Get user's current subscription
    const { data: subscription, error: subscriptionError } = await supabase
      .from('subscriptions')
      .select('*')
      .eq('user_id', userId)
      .eq('status', 'active')
      .order('created_at', { ascending: false })
      .limit(1)
      .single();

    if (subscriptionError || !subscription) {
      console.error('❌ No active subscription found:', subscriptionError);
      return NextResponse.json(
        { error: 'No active subscription found' },
        { status: 404 }
      );
    }

    const currentTier = subscription.tier;
    console.log('📊 Current tier:', currentTier);

    // Validate this is actually a downgrade
    const tierOrder = { 'free': 0, 'starter': 1, 'professional': 2 };
    const currentTierValue = tierOrder[currentTier as keyof typeof tierOrder];
    const newTierValue = tierOrder[newTier as keyof typeof tierOrder];

    if (newTierValue >= currentTierValue) {
      return NextResponse.json(
        { error: 'This is not a downgrade. Use checkout for upgrades.' },
        { status: 400 }
      );
    }

    // Handle downgrade to free tier
    if (newTier === 'free') {
      console.log('📉 Downgrading to free tier - canceling subscription');
      
      // Cancel the Stripe subscription
      if (subscription.stripe_subscription_id) {
        await stripe.subscriptions.update(subscription.stripe_subscription_id, {
          cancel_at_period_end: true,
        });
        console.log('✅ Stripe subscription set to cancel at period end');
      }

      // Update subscription record to show it will cancel
      const { error: updateError } = await supabase
        .from('subscriptions')
        .update({
          cancel_at_period_end: true,
          updated_at: new Date().toISOString(),
        })
        .eq('id', subscription.id);

      if (updateError) {
        console.error('❌ Error updating subscription:', updateError);
        return NextResponse.json(
          { error: 'Failed to update subscription' },
          { status: 500 }
        );
      }

      // Update user profile immediately to free tier
      const { error: profileError } = await supabase
        .from('user_profiles')
        .update({ 
          subscription_tier: 'free',
          updated_at: new Date().toISOString()
        })
        .eq('id', userId);

      if (profileError) {
        console.error('❌ Error updating user profile:', profileError);
        return NextResponse.json(
          { error: 'Failed to update user profile' },
          { status: 500 }
        );
      }

      console.log('✅ Successfully downgraded to free tier');
      return NextResponse.json({
        success: true,
        message: 'Successfully downgraded to free tier. Your subscription will cancel at the end of the current billing period.',
        newTier: 'free'
      });
    }

    // Handle downgrade between paid tiers (starter ↔ professional)
    console.log('📉 Downgrading between paid tiers');
    
    // Get the new price ID for the target tier
    const newTierConfig = TIER_CONFIGS[newTier as SubscriptionTier];
    if (!newTierConfig.priceId) {
      return NextResponse.json(
        { error: 'Invalid price ID for target tier' },
        { status: 400 }
      );
    }

    // Update the Stripe subscription to the new price
    if (subscription.stripe_subscription_id) {
      const stripeSubscription = await stripe.subscriptions.retrieve(subscription.stripe_subscription_id);
      
      await stripe.subscriptions.update(subscription.stripe_subscription_id, {
        items: [{
          id: stripeSubscription.items.data[0].id,
          price: newTierConfig.priceId,
        }],
        proration_behavior: 'create_prorations', // This will handle the proration
      });
      console.log('✅ Stripe subscription updated to new tier');
    }

    // Update subscription record
    const { error: updateError } = await supabase
      .from('subscriptions')
      .update({
        tier: newTier,
        updated_at: new Date().toISOString(),
      })
      .eq('id', subscription.id);

    if (updateError) {
      console.error('❌ Error updating subscription:', updateError);
      return NextResponse.json(
        { error: 'Failed to update subscription' },
        { status: 500 }
      );
    }

    // Update user profile
    const { error: profileError } = await supabase
      .from('user_profiles')
      .update({ 
        subscription_tier: newTier,
        updated_at: new Date().toISOString()
      })
      .eq('id', userId);

    if (profileError) {
      console.error('❌ Error updating user profile:', profileError);
      return NextResponse.json(
        { error: 'Failed to update user profile' },
        { status: 500 }
      );
    }

    console.log('✅ Successfully downgraded to', newTier);
    return NextResponse.json({
      success: true,
      message: `Successfully downgraded to ${newTierConfig.name} tier.`,
      newTier
    });

  } catch (error) {
    console.error('💥 Error processing downgrade:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
