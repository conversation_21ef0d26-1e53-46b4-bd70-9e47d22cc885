/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/stripe/customer-portal/route";
exports.ids = ["app/api/stripe/customer-portal/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive":
/*!************************************************************!*\
  !*** ./node_modules/@supabase/realtime-js/dist/main/ sync ***!
  \************************************************************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive";
module.exports = webpackEmptyContext;

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fstripe%2Fcustomer-portal%2Froute&page=%2Fapi%2Fstripe%2Fcustomer-portal%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstripe%2Fcustomer-portal%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fstripe%2Fcustomer-portal%2Froute&page=%2Fapi%2Fstripe%2Fcustomer-portal%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstripe%2Fcustomer-portal%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_RoKey_App_rokey_app_src_app_api_stripe_customer_portal_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/stripe/customer-portal/route.ts */ \"(rsc)/./src/app/api/stripe/customer-portal/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/stripe/customer-portal/route\",\n        pathname: \"/api/stripe/customer-portal\",\n        filename: \"route\",\n        bundlePath: \"app/api/stripe/customer-portal/route\"\n    },\n    resolvedPagePath: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\api\\\\stripe\\\\customer-portal\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_RoKey_App_rokey_app_src_app_api_stripe_customer_portal_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fstripe%2Fcustomer-portal%2Froute&page=%2Fapi%2Fstripe%2Fcustomer-portal%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstripe%2Fcustomer-portal%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/stripe/customer-portal/route.ts":
/*!*****************************************************!*\
  !*** ./src/app/api/stripe/customer-portal/route.ts ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var stripe__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! stripe */ \"(rsc)/./node_modules/stripe/esm/stripe.esm.node.js\");\n/* harmony import */ var _barrel_optimize_names_createClient_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=createClient!=!@supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n/* harmony import */ var _lib_stripe_config__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/stripe-config */ \"(rsc)/./src/lib/stripe-config.ts\");\n\n\n\n\nconsole.log('🔧 Stripe configuration:', {\n    environment: \"development\",\n    secretKeyPrefix: _lib_stripe_config__WEBPACK_IMPORTED_MODULE_2__.STRIPE_KEYS.secretKey?.substring(0, 15) + '...',\n    hasSecretKey: !!_lib_stripe_config__WEBPACK_IMPORTED_MODULE_2__.STRIPE_KEYS.secretKey\n});\nconst stripe = new stripe__WEBPACK_IMPORTED_MODULE_1__[\"default\"](_lib_stripe_config__WEBPACK_IMPORTED_MODULE_2__.STRIPE_KEYS.secretKey, {\n    apiVersion: '2025-02-24.acacia'\n});\nconst supabase = (0,_barrel_optimize_names_createClient_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_3__.createClient)(\"https://hpkzzhpufhbxtxqaugjh.supabase.co\", process.env.SUPABASE_SERVICE_ROLE_KEY);\nasync function POST(req) {\n    try {\n        const body = await req.json();\n        const { userId, returnUrl } = body;\n        console.log('🔄 Customer portal request received');\n        console.log('👤 User ID:', userId);\n        console.log('🔗 Return URL:', returnUrl);\n        console.log('🌍 Environment:', \"development\");\n        if (!userId) {\n            console.error('❌ Missing userId in request');\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Missing userId'\n            }, {\n                status: 400\n            });\n        }\n        // Get user's subscription record to find their Stripe customer ID\n        // We look for any subscription record (active, canceled, etc.) since we need the customer ID\n        const { data: subscription, error: subscriptionError } = await supabase.from('subscriptions').select('stripe_customer_id').eq('user_id', userId).order('created_at', {\n            ascending: false\n        }).limit(1).single();\n        let stripeCustomerId = null;\n        if (subscription && subscription.stripe_customer_id) {\n            stripeCustomerId = subscription.stripe_customer_id;\n        } else {\n            // Fallback: Try to find customer by email in Stripe\n            console.log('No subscription record found, trying to find customer by email...');\n            // Get user email from auth\n            const { data: userData, error: userError } = await supabase.auth.admin.getUserById(userId);\n            if (userError || !userData.user?.email) {\n                console.error('Could not get user email:', userError);\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'No Stripe customer found for user'\n                }, {\n                    status: 404\n                });\n            }\n            // Search for existing Stripe customer by email\n            try {\n                const existingCustomers = await stripe.customers.list({\n                    email: userData.user.email,\n                    limit: 1\n                });\n                if (existingCustomers.data.length > 0) {\n                    stripeCustomerId = existingCustomers.data[0].id;\n                    console.log('Found existing Stripe customer by email:', stripeCustomerId);\n                } else {\n                    // Create a new Stripe customer if none exists\n                    console.log('Creating new Stripe customer for user...');\n                    const customer = await stripe.customers.create({\n                        email: userData.user.email,\n                        metadata: {\n                            supabase_user_id: userId\n                        }\n                    });\n                    stripeCustomerId = customer.id;\n                    console.log('Created new Stripe customer:', stripeCustomerId);\n                }\n            } catch (stripeError) {\n                console.error('Error handling Stripe customer:', stripeError);\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'Failed to create or find Stripe customer'\n                }, {\n                    status: 500\n                });\n            }\n        }\n        if (!stripeCustomerId) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'No Stripe customer found for user'\n            }, {\n                status: 404\n            });\n        }\n        // Create customer portal session with dynamic return URL\n        const finalReturnUrl = returnUrl || `${ true ? 'http://localhost:3000' : 0}/billing`;\n        console.log('Creating portal session for customer:', stripeCustomerId);\n        const portalSession = await stripe.billingPortal.sessions.create({\n            customer: stripeCustomerId,\n            return_url: finalReturnUrl\n        });\n        console.log('Portal session created successfully:', portalSession.id);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            url: portalSession.url\n        });\n    } catch (error) {\n        console.error('Error creating customer portal session:', error);\n        if (error instanceof stripe__WEBPACK_IMPORTED_MODULE_1__[\"default\"].errors.StripeError) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: `Stripe error: ${error.message}`\n            }, {\n                status: 400\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/stripe/customer-portal/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/stripe-config.ts":
/*!**********************************!*\
  !*** ./src/lib/stripe-config.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PUBLIC_STRIPE_PRICE_IDS: () => (/* binding */ PUBLIC_STRIPE_PRICE_IDS),\n/* harmony export */   STRIPE_ENV_INFO: () => (/* binding */ STRIPE_ENV_INFO),\n/* harmony export */   STRIPE_KEYS: () => (/* binding */ STRIPE_KEYS),\n/* harmony export */   STRIPE_PRICE_IDS: () => (/* binding */ STRIPE_PRICE_IDS),\n/* harmony export */   STRIPE_PRODUCT_IDS: () => (/* binding */ STRIPE_PRODUCT_IDS)\n/* harmony export */ });\n// Stripe Configuration with Environment Detection\n// Automatically switches between test and live keys based on environment\nconst isProduction = \"development\" === 'production';\n// Stripe Keys - Auto-selected based on environment\nconst STRIPE_KEYS = {\n    publishableKey: isProduction ? process.env.STRIPE_LIVE_PUBLISHABLE_KEY : process.env.STRIPE_TEST_PUBLISHABLE_KEY,\n    secretKey: isProduction ? process.env.STRIPE_LIVE_SECRET_KEY : process.env.STRIPE_TEST_SECRET_KEY,\n    webhookSecret: isProduction ? process.env.STRIPE_LIVE_WEBHOOK_SECRET : process.env.STRIPE_TEST_WEBHOOK_SECRET\n};\n// Stripe Price IDs - Auto-selected based on environment\nconst STRIPE_PRICE_IDS = {\n    FREE: isProduction ? process.env.STRIPE_LIVE_FREE_PRICE_ID : process.env.STRIPE_TEST_FREE_PRICE_ID,\n    STARTER: isProduction ? process.env.STRIPE_LIVE_STARTER_PRICE_ID : process.env.STRIPE_TEST_STARTER_PRICE_ID,\n    PROFESSIONAL: isProduction ? process.env.STRIPE_LIVE_PROFESSIONAL_PRICE_ID : process.env.STRIPE_TEST_PROFESSIONAL_PRICE_ID,\n    ENTERPRISE: isProduction ? process.env.STRIPE_LIVE_ENTERPRISE_PRICE_ID : process.env.STRIPE_TEST_ENTERPRISE_PRICE_ID\n};\n// Stripe Product IDs - Auto-selected based on environment\nconst STRIPE_PRODUCT_IDS = {\n    FREE: isProduction ? process.env.STRIPE_LIVE_FREE_PRODUCT_ID : process.env.STRIPE_TEST_FREE_PRODUCT_ID,\n    STARTER: isProduction ? process.env.STRIPE_LIVE_STARTER_PRODUCT_ID : process.env.STRIPE_TEST_STARTER_PRODUCT_ID,\n    PROFESSIONAL: isProduction ? process.env.STRIPE_LIVE_PROFESSIONAL_PRODUCT_ID : process.env.STRIPE_TEST_PROFESSIONAL_PRODUCT_ID,\n    ENTERPRISE: isProduction ? process.env.STRIPE_LIVE_ENTERPRISE_PRODUCT_ID : process.env.STRIPE_TEST_ENTERPRISE_PRODUCT_ID\n};\n// Public Price IDs for frontend (auto-selected based on environment)\nconst PUBLIC_STRIPE_PRICE_IDS = {\n    FREE: isProduction ? process.env.STRIPE_LIVE_FREE_PRICE_ID : process.env.STRIPE_TEST_FREE_PRICE_ID,\n    STARTER: isProduction ? process.env.STRIPE_LIVE_STARTER_PRICE_ID : process.env.STRIPE_TEST_STARTER_PRICE_ID,\n    PROFESSIONAL: isProduction ? process.env.STRIPE_LIVE_PROFESSIONAL_PRICE_ID : process.env.STRIPE_TEST_PROFESSIONAL_PRICE_ID,\n    ENTERPRISE: isProduction ? process.env.STRIPE_LIVE_ENTERPRISE_PRICE_ID : process.env.STRIPE_TEST_ENTERPRISE_PRICE_ID\n};\n// Environment info for debugging\nconst STRIPE_ENV_INFO = {\n    isProduction,\n    environment: isProduction ? 'LIVE' : 'TEST',\n    keysUsed: {\n        publishable: STRIPE_KEYS.publishableKey ? STRIPE_KEYS.publishableKey.substring(0, 20) + '...' : 'undefined',\n        secret: STRIPE_KEYS.secretKey ? STRIPE_KEYS.secretKey.substring(0, 20) + '...' : 'undefined',\n        webhook: STRIPE_KEYS.webhookSecret ? STRIPE_KEYS.webhookSecret.substring(0, 15) + '...' : 'undefined'\n    }\n};\n// Log environment info in development\nif (!isProduction) {\n    console.log('🔧 Stripe Environment:', STRIPE_ENV_INFO.environment);\n    console.log('🔑 Using keys:', STRIPE_ENV_INFO.keysUsed);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/stripe-config.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "child_process":
/*!********************************!*\
  !*** external "child_process" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = require("child_process");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions","vendor-chunks/stripe","vendor-chunks/math-intrinsics","vendor-chunks/es-errors","vendor-chunks/qs","vendor-chunks/call-bind-apply-helpers","vendor-chunks/get-proto","vendor-chunks/object-inspect","vendor-chunks/has-symbols","vendor-chunks/gopd","vendor-chunks/function-bind","vendor-chunks/side-channel","vendor-chunks/side-channel-weakmap","vendor-chunks/side-channel-map","vendor-chunks/side-channel-list","vendor-chunks/hasown","vendor-chunks/get-intrinsic","vendor-chunks/es-object-atoms","vendor-chunks/es-define-property","vendor-chunks/dunder-proto","vendor-chunks/call-bound"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fstripe%2Fcustomer-portal%2Froute&page=%2Fapi%2Fstripe%2Fcustomer-portal%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstripe%2Fcustomer-portal%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();