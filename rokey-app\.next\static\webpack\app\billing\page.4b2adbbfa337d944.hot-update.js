"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/billing/page",{

/***/ "(app-pages-browser)/./src/app/billing/page.tsx":
/*!**********************************!*\
  !*** ./src/app/billing/page.tsx ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ BillingPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_EnvelopeIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpIcon,CalendarIcon,CheckCircleIcon,CreditCardIcon,EnvelopeIcon,ExclamationTriangleIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CreditCardIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_EnvelopeIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpIcon,CalendarIcon,CheckCircleIcon,CreditCardIcon,EnvelopeIcon,ExclamationTriangleIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CalendarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_EnvelopeIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpIcon,CalendarIcon,CheckCircleIcon,CreditCardIcon,EnvelopeIcon,ExclamationTriangleIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CheckCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_EnvelopeIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpIcon,CalendarIcon,CheckCircleIcon,CreditCardIcon,EnvelopeIcon,ExclamationTriangleIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/XCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_EnvelopeIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpIcon,CalendarIcon,CheckCircleIcon,CreditCardIcon,EnvelopeIcon,ExclamationTriangleIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowUpIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_EnvelopeIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpIcon,CalendarIcon,CheckCircleIcon,CreditCardIcon,EnvelopeIcon,ExclamationTriangleIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ExclamationTriangleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_EnvelopeIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpIcon,CalendarIcon,CheckCircleIcon,CreditCardIcon,EnvelopeIcon,ExclamationTriangleIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/EnvelopeIcon.js\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/Button */ \"(app-pages-browser)/./src/components/ui/Button.tsx\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _hooks_useSubscription__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/hooks/useSubscription */ \"(app-pages-browser)/./src/hooks/useSubscription.ts\");\n/* harmony import */ var _components_ui_ConfirmationModal__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/ConfirmationModal */ \"(app-pages-browser)/./src/components/ui/ConfirmationModal.tsx\");\n/* harmony import */ var canvas_confetti__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! canvas-confetti */ \"(app-pages-browser)/./node_modules/canvas-confetti/dist/confetti.module.mjs\");\n/* harmony import */ var _hooks_useConfirmation__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/useConfirmation */ \"(app-pages-browser)/./src/hooks/useConfirmation.ts\");\n/* harmony import */ var _emailjs_browser__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @emailjs/browser */ \"(app-pages-browser)/./node_modules/@emailjs/browser/es/index.js\");\n/* harmony import */ var _lib_stripe_client__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/lib/stripe-client */ \"(app-pages-browser)/./src/lib/stripe-client.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n// Convert TIER_CONFIGS to plan format for the UI\nconst plans = Object.entries(_lib_stripe_client__WEBPACK_IMPORTED_MODULE_10__.TIER_CONFIGS).map((param)=>{\n    let [tier, config] = param;\n    return {\n        id: tier,\n        name: config.name,\n        price: parseInt(config.price.replace('$', '')),\n        interval: tier === 'free' ? 'forever' : 'month',\n        features: config.features,\n        popular: tier === 'starter' // Mark starter as popular\n    };\n});\nfunction BillingPage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const { user, subscriptionStatus, refreshStatus: refreshSubscription, createCheckoutSession, openCustomerPortal, downgradePlan } = (0,_hooks_useSubscription__WEBPACK_IMPORTED_MODULE_5__.useSubscription)();\n    const confirmation = (0,_hooks_useConfirmation__WEBPACK_IMPORTED_MODULE_8__.useConfirmation)();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showCancelModal, setShowCancelModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [cancelReason, setCancelReason] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [cancelFeedback, setCancelFeedback] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [tierBeforePortal, setTierBeforePortal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const currentPlan = plans.find((plan)=>plan.id === (subscriptionStatus === null || subscriptionStatus === void 0 ? void 0 : subscriptionStatus.tier)) || plans[0];\n    // Calculate days until renewal based on actual subscription data\n    const daysUntilRenewal = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"BillingPage.useMemo[daysUntilRenewal]\": ()=>{\n            if ((subscriptionStatus === null || subscriptionStatus === void 0 ? void 0 : subscriptionStatus.tier) === 'free' || !(subscriptionStatus === null || subscriptionStatus === void 0 ? void 0 : subscriptionStatus.currentPeriodEnd)) {\n                return null;\n            }\n            const renewalDate = new Date(subscriptionStatus.currentPeriodEnd);\n            const today = new Date();\n            const diffTime = renewalDate.getTime() - today.getTime();\n            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n            // Return null if subscription has already expired\n            return diffDays > 0 ? diffDays : null;\n        }\n    }[\"BillingPage.useMemo[daysUntilRenewal]\"], [\n        subscriptionStatus === null || subscriptionStatus === void 0 ? void 0 : subscriptionStatus.tier,\n        subscriptionStatus === null || subscriptionStatus === void 0 ? void 0 : subscriptionStatus.currentPeriodEnd\n    ]);\n    // Helper function to determine if a tier change is an upgrade\n    const isUpgrade = (fromTier, toTier)=>{\n        const tierOrder = {\n            'free': 0,\n            'starter': 1,\n            'professional': 2,\n            'enterprise': 3\n        };\n        return tierOrder[toTier] > tierOrder[fromTier];\n    };\n    // Handle return from Customer Portal\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"BillingPage.useEffect\": ()=>{\n            const portalReturn = searchParams.get('portal_return');\n            const previousTier = searchParams.get('prev_tier');\n            if (portalReturn === 'true') {\n                console.log('Returned from Customer Portal, previous tier:', previousTier);\n                // Remove the parameters from URL\n                const newUrl = new URL(window.location.href);\n                newUrl.searchParams.delete('portal_return');\n                newUrl.searchParams.delete('prev_tier');\n                window.history.replaceState({}, '', newUrl.toString());\n                // Wait for webhook to process, then refresh subscription status\n                const refreshWithRetry = {\n                    \"BillingPage.useEffect.refreshWithRetry\": async ()=>{\n                        let attempts = 0;\n                        const maxAttempts = 8; // Increased attempts\n                        const initialDelay = 3000; // Longer initial delay for webhook processing\n                        const retryDelay = 2000; // 2 seconds between retry attempts\n                        console.log('Starting subscription refresh with retry logic...');\n                        // Initial delay to allow webhook processing\n                        await new Promise({\n                            \"BillingPage.useEffect.refreshWithRetry\": (resolve)=>setTimeout(resolve, initialDelay)\n                        }[\"BillingPage.useEffect.refreshWithRetry\"]);\n                        while(attempts < maxAttempts){\n                            try {\n                                console.log(\"Refresh attempt \".concat(attempts + 1, \"/\").concat(maxAttempts));\n                                // Force a complete refresh\n                                await refreshSubscription();\n                                // Wait a moment for the state to update\n                                await new Promise({\n                                    \"BillingPage.useEffect.refreshWithRetry\": (resolve)=>setTimeout(resolve, 500)\n                                }[\"BillingPage.useEffect.refreshWithRetry\"]);\n                                // Get fresh subscription status after refresh\n                                const response = await fetch(\"/api/stripe/subscription-status?userId=\".concat(user === null || user === void 0 ? void 0 : user.id, \"&_t=\").concat(Date.now()), {\n                                    cache: 'no-store',\n                                    headers: {\n                                        'Cache-Control': 'no-cache, no-store, must-revalidate',\n                                        'Pragma': 'no-cache',\n                                        'Expires': '0'\n                                    }\n                                });\n                                if (response.ok) {\n                                    const freshStatus = await response.json();\n                                    const currentTier = freshStatus.tier || 'free';\n                                    console.log('Fresh tier from API:', currentTier, 'Previous tier:', previousTier);\n                                    // Check if tier actually changed\n                                    if (currentTier !== previousTier) {\n                                        const wasUpgrade = isUpgrade(previousTier || 'free', currentTier);\n                                        if (wasUpgrade) {\n                                            // Show upgrade success message with confetti\n                                            sonner__WEBPACK_IMPORTED_MODULE_4__.toast.success('Plan upgraded successfully!');\n                                            // Trigger confetti for upgrades only\n                                            const triggerConfetti = {\n                                                \"BillingPage.useEffect.refreshWithRetry.triggerConfetti\": ()=>{\n                                                    (0,canvas_confetti__WEBPACK_IMPORTED_MODULE_7__[\"default\"])({\n                                                        particleCount: 100,\n                                                        spread: 70,\n                                                        origin: {\n                                                            y: 0.6\n                                                        }\n                                                    });\n                                                }\n                                            }[\"BillingPage.useEffect.refreshWithRetry.triggerConfetti\"];\n                                            triggerConfetti();\n                                            setTimeout(triggerConfetti, 500);\n                                            setTimeout(triggerConfetti, 1000);\n                                        } else {\n                                            // Show generic success message for downgrades/cancellations\n                                            sonner__WEBPACK_IMPORTED_MODULE_4__.toast.success('Billing settings updated successfully!');\n                                        }\n                                        console.log('Plan change detected and processed successfully');\n                                        break;\n                                    } else if (attempts >= 3) {\n                                        // After a few attempts, show success message even if no change detected\n                                        // (user might have just viewed the portal without making changes)\n                                        sonner__WEBPACK_IMPORTED_MODULE_4__.toast.success('Billing settings updated successfully!');\n                                        console.log('No plan change detected, but showing success message');\n                                        break;\n                                    }\n                                }\n                                attempts++;\n                                if (attempts < maxAttempts) {\n                                    console.log(\"No change detected yet, waiting \".concat(retryDelay, \"ms before next attempt...\"));\n                                    await new Promise({\n                                        \"BillingPage.useEffect.refreshWithRetry\": (resolve)=>setTimeout(resolve, retryDelay)\n                                    }[\"BillingPage.useEffect.refreshWithRetry\"]);\n                                }\n                            } catch (error) {\n                                console.error(\"Refresh attempt \".concat(attempts + 1, \" failed:\"), error);\n                                attempts++;\n                                if (attempts >= maxAttempts) {\n                                    console.log('Max refresh attempts reached, forcing page reload...');\n                                    sonner__WEBPACK_IMPORTED_MODULE_4__.toast.error('Refreshing page to update subscription status...');\n                                    // Force a page reload as last resort\n                                    setTimeout({\n                                        \"BillingPage.useEffect.refreshWithRetry\": ()=>{\n                                            window.location.reload();\n                                        }\n                                    }[\"BillingPage.useEffect.refreshWithRetry\"], 2000);\n                                } else {\n                                    await new Promise({\n                                        \"BillingPage.useEffect.refreshWithRetry\": (resolve)=>setTimeout(resolve, retryDelay)\n                                    }[\"BillingPage.useEffect.refreshWithRetry\"]);\n                                }\n                            }\n                        }\n                    }\n                }[\"BillingPage.useEffect.refreshWithRetry\"];\n                refreshWithRetry();\n            }\n        }\n    }[\"BillingPage.useEffect\"], [\n        searchParams,\n        refreshSubscription,\n        user === null || user === void 0 ? void 0 : user.id\n    ]); // Removed subscriptionStatus?.tier from dependencies\n    const handleChangePlan = async ()=>{\n        console.log('🎯 Manage Subscription button clicked');\n        console.log('👤 User:', user === null || user === void 0 ? void 0 : user.id);\n        console.log('📊 Subscription status:', subscriptionStatus);\n        // For existing users (anyone who can access the billing page), always use Customer Portal\n        // The billing page is only accessible to authenticated users with accounts\n        try {\n            setLoading(true);\n            console.log('⏳ Setting loading state to true');\n            // Create return URL with current domain, portal return parameter, and current tier\n            const currentTier = (subscriptionStatus === null || subscriptionStatus === void 0 ? void 0 : subscriptionStatus.tier) || 'free';\n            const returnUrl = \"\".concat(window.location.origin, \"/billing?portal_return=true&prev_tier=\").concat(currentTier);\n            console.log('🔗 Generated return URL:', returnUrl);\n            await openCustomerPortal(returnUrl);\n        } catch (error) {\n            console.error('💥 Customer portal error:', error);\n            // If portal is not configured, fall back to showing a helpful message\n            if (error.message.includes('No configuration provided') || error.message.includes('default configuration has not been created')) {\n                sonner__WEBPACK_IMPORTED_MODULE_4__.toast.error('Billing portal is being set up. Please contact support for plan changes.');\n            } else {\n                sonner__WEBPACK_IMPORTED_MODULE_4__.toast.error('Failed to open billing portal. Please try again.');\n            }\n            setLoading(false);\n            console.log('⏳ Setting loading state to false after error');\n        }\n    };\n    const handleCancelSubscription = async ()=>{\n        if (!cancelReason.trim()) {\n            sonner__WEBPACK_IMPORTED_MODULE_4__.toast.error('Please select a reason for cancellation');\n            return;\n        }\n        setLoading(true);\n        try {\n            var _user_user_metadata;\n            // Send cancellation feedback via EmailJS\n            const templateParams = {\n                user_email: (user === null || user === void 0 ? void 0 : user.email) || 'Unknown',\n                user_name: (user === null || user === void 0 ? void 0 : (_user_user_metadata = user.user_metadata) === null || _user_user_metadata === void 0 ? void 0 : _user_user_metadata.first_name) || 'User',\n                current_plan: currentPlan.name,\n                cancel_reason: cancelReason,\n                additional_feedback: cancelFeedback,\n                cancel_date: new Date().toLocaleDateString()\n            };\n            await _emailjs_browser__WEBPACK_IMPORTED_MODULE_9__[\"default\"].send(\"service_2xtn7iv\", \"template_pg7e1af\", templateParams, \"lm7-ATth2Cql60KIN\");\n            // Here you would also cancel the subscription in your payment processor\n            // For now, we'll simulate the process\n            await new Promise((resolve)=>setTimeout(resolve, 1500));\n            sonner__WEBPACK_IMPORTED_MODULE_4__.toast.success('Subscription cancelled successfully. We\\'ve sent your feedback to our team.');\n            setShowCancelModal(false);\n            setCancelReason('');\n            setCancelFeedback('');\n            await refreshSubscription();\n        } catch (error) {\n            console.error('Cancellation error:', error);\n            sonner__WEBPACK_IMPORTED_MODULE_4__.toast.error('Failed to cancel subscription. Please contact support.');\n        } finally{\n            setLoading(false);\n        }\n    };\n    const cancelReasons = [\n        'Too expensive',\n        'Not using enough features',\n        'Found a better alternative',\n        'Technical issues',\n        'Poor customer support',\n        'Missing features I need',\n        'Temporary financial constraints',\n        'Other'\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen w-full bg-[#040716] text-white overflow-x-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-b border-gray-800/50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full px-4 sm:px-6 lg:px-8 py-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-2xl font-semibold text-white\",\n                                        children: \"Billing & Plans\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                        lineNumber: 297,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-1\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"px-3 py-1 text-sm bg-cyan-500 text-white rounded\",\n                                            children: \"Subscription\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                            lineNumber: 299,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                        lineNumber: 298,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                lineNumber: 296,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                lineNumber: 304,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                        lineNumber: 295,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                    lineNumber: 294,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                lineNumber: 293,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full px-4 sm:px-6 lg:px-8 py-8 space-y-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-900/50 backdrop-blur-sm rounded-lg border border-gray-800/50 p-6 hover:shadow-lg transition-all duration-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-2 bg-cyan-500/10 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_EnvelopeIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"h-5 w-5 text-cyan-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                    lineNumber: 321,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 320,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-lg font-semibold text-white\",\n                                                children: \"Current Plan\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 323,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                        lineNumber: 319,\n                                        columnNumber: 13\n                                    }, this),\n                                    (subscriptionStatus === null || subscriptionStatus === void 0 ? void 0 : subscriptionStatus.tier) !== 'free' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"outline\",\n                                        onClick: ()=>setShowCancelModal(true),\n                                        className: \"text-red-400 border-red-500/20 hover:bg-red-500/10 hover:border-red-500/40 transition-all duration-200 text-sm\",\n                                        size: \"sm\",\n                                        children: \"Cancel Subscription\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                        lineNumber: 326,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                lineNumber: 318,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"md:col-span-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-4 mb-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-2xl font-bold text-white\",\n                                                        children: currentPlan.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                        lineNumber: 341,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    currentPlan.popular && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"px-2 py-1 text-xs bg-gradient-to-r from-orange-500 to-orange-600 text-white rounded-full font-semibold\",\n                                                        children: \"⭐ Popular\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                        lineNumber: 343,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 340,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-3xl font-bold text-white\",\n                                                        children: currentPlan.price === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-green-400\",\n                                                            children: \"Free\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                            lineNumber: 351,\n                                                            columnNumber: 21\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                \"$\",\n                                                                currentPlan.price,\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-400 text-lg font-normal\",\n                                                                    children: [\n                                                                        \"/\",\n                                                                        currentPlan.interval\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                                    lineNumber: 353,\n                                                                    columnNumber: 47\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                            lineNumber: 353,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                        lineNumber: 349,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    currentPlan.price === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-green-400 font-medium\",\n                                                        children: \"Forever free\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                        lineNumber: 357,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 348,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                        lineNumber: 339,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col justify-center\",\n                                        children: daysUntilRenewal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gray-800/50 rounded-lg p-4 text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-center gap-2 text-sm text-gray-300 mb-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_EnvelopeIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                            lineNumber: 367,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Next Billing\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                            lineNumber: 368,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                    lineNumber: 366,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-white font-semibold\",\n                                                    children: [\n                                                        daysUntilRenewal,\n                                                        \" days\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                    lineNumber: 370,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                            lineNumber: 365,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                        lineNumber: 363,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                lineNumber: 337,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                        lineNumber: 317,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-900/50 backdrop-blur-sm rounded-lg border border-gray-800/50 p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3 mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-2 bg-blue-500/10 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_EnvelopeIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"h-5 w-5 text-blue-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                    lineNumber: 385,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 384,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-white\",\n                                                children: \"Plan Features\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 387,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                        lineNumber: 383,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: currentPlan.features.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-3 py-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-shrink-0\",\n                                                        children: feature.included ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-1 bg-green-500/10 rounded-full\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_EnvelopeIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"h-4 w-4 text-green-400\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                                lineNumber: 396,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                            lineNumber: 395,\n                                                            columnNumber: 23\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-1 bg-gray-700/50 rounded-full\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_EnvelopeIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"h-4 w-4 text-gray-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                                lineNumber: 400,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                            lineNumber: 399,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                        lineNumber: 393,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm \".concat(feature.included ? 'text-gray-300' : 'text-gray-500'),\n                                                        children: [\n                                                            feature.name,\n                                                            feature.limit && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-400 font-medium\",\n                                                                children: [\n                                                                    \" (\",\n                                                                    feature.limit,\n                                                                    \")\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                                lineNumber: 409,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                        lineNumber: 404,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, index, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 392,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                        lineNumber: 390,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                lineNumber: 382,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-900/50 backdrop-blur-sm rounded-lg border border-gray-800/50 p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3 mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-2 bg-orange-500/10 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_EnvelopeIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"h-5 w-5 text-orange-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                    lineNumber: 421,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 420,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-white\",\n                                                children: \"Manage Subscription\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 423,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                        lineNumber: 419,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center p-4 bg-gray-800/30 rounded-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"inline-flex items-center gap-2 bg-green-500/10 text-green-400 px-3 py-1 rounded-full text-sm font-semibold mb-3 border border-green-500/20\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_EnvelopeIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                                lineNumber: 430,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            \"Active Plan\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                        lineNumber: 429,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"text-xl font-bold text-white mb-1\",\n                                                        children: currentPlan.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                        lineNumber: 433,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl font-bold text-white\",\n                                                        children: currentPlan.price === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-green-400\",\n                                                            children: \"Free\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                            lineNumber: 436,\n                                                            columnNumber: 21\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                \"$\",\n                                                                currentPlan.price,\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-400 text-base font-normal\",\n                                                                    children: [\n                                                                        \"/\",\n                                                                        currentPlan.interval\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                                    lineNumber: 438,\n                                                                    columnNumber: 47\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                            lineNumber: 438,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                        lineNumber: 434,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 428,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                onClick: handleChangePlan,\n                                                disabled: loading,\n                                                className: \"w-full bg-gradient-to-r from-cyan-500 to-cyan-600 hover:from-cyan-600 hover:to-cyan-700 text-white shadow-lg hover:shadow-xl transition-all duration-200\",\n                                                size: \"lg\",\n                                                children: (subscriptionStatus === null || subscriptionStatus === void 0 ? void 0 : subscriptionStatus.tier) === 'free' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_EnvelopeIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            className: \"h-5 w-5 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                            lineNumber: 452,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"Upgrade Plan\"\n                                                    ]\n                                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_EnvelopeIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: \"h-5 w-5 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                            lineNumber: 457,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"Manage Subscription\"\n                                                    ]\n                                                }, void 0, true)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 444,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-center text-sm text-gray-400\",\n                                                children: (subscriptionStatus === null || subscriptionStatus === void 0 ? void 0 : subscriptionStatus.tier) === 'free' ? 'Unlock advanced features and higher limits' : 'Change plans, update billing, or cancel anytime'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 463,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                        lineNumber: 426,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                lineNumber: 418,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                        lineNumber: 380,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-900/50 backdrop-blur-sm rounded-lg border border-gray-800/50 p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3 mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-2 bg-green-500/10 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_EnvelopeIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"h-5 w-5 text-green-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                    lineNumber: 479,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 478,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-white\",\n                                                children: \"Billing Details\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 481,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                        lineNumber: 477,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center py-2 border-b border-gray-700/30\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-400 text-sm\",\n                                                        children: \"Plan\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                        lineNumber: 486,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium text-white\",\n                                                        children: currentPlan.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                        lineNumber: 487,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 485,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center py-2 border-b border-gray-700/30\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-400 text-sm\",\n                                                        children: \"Billing Cycle\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                        lineNumber: 490,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium text-white\",\n                                                        children: currentPlan.price === 0 ? 'N/A' : \"Monthly\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                        lineNumber: 491,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 489,\n                                                columnNumber: 15\n                                            }, this),\n                                            daysUntilRenewal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center py-2 border-b border-gray-700/30\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-400 text-sm\",\n                                                        children: \"Next Billing\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                        lineNumber: 497,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium text-white\",\n                                                        children: new Date(Date.now() + daysUntilRenewal * 24 * 60 * 60 * 1000).toLocaleDateString()\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                        lineNumber: 498,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 496,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center py-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-400 text-sm\",\n                                                        children: \"Status\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                        lineNumber: 504,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"px-2 py-1 text-xs rounded-full font-medium \".concat((subscriptionStatus === null || subscriptionStatus === void 0 ? void 0 : subscriptionStatus.tier) === 'free' ? 'bg-gray-700/50 text-gray-300' : 'bg-green-500/10 text-green-400'),\n                                                        children: (subscriptionStatus === null || subscriptionStatus === void 0 ? void 0 : subscriptionStatus.tier) === 'free' ? 'Free Plan' : 'Active'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                        lineNumber: 505,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 503,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                        lineNumber: 484,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                lineNumber: 476,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-900/50 backdrop-blur-sm rounded-lg border border-gray-800/50 p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3 mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-2 bg-purple-500/10 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_EnvelopeIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"h-5 w-5 text-purple-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                    lineNumber: 520,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 519,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-white\",\n                                                children: \"Support\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 522,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                        lineNumber: 518,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-300 text-sm\",\n                                                children: \"Need help with your subscription or billing? We're here to assist you.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 526,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        variant: \"outline\",\n                                                        className: \"w-full justify-start text-gray-300 border-gray-600 hover:bg-gray-800/50 hover:border-gray-500 text-sm\",\n                                                        onClick: ()=>window.open('/contact', '_blank'),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_EnvelopeIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                className: \"h-4 w-4 mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                                lineNumber: 535,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            \"Contact Support\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                        lineNumber: 530,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-gray-400 text-center\",\n                                                        children: \"Response time: Usually within 24 hours\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                        lineNumber: 538,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 529,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                        lineNumber: 525,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                lineNumber: 517,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                        lineNumber: 474,\n                        columnNumber: 9\n                    }, this),\n                    showCancelModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center p-4 z-50\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-900/95 backdrop-blur-sm border border-gray-800/50 rounded-xl shadow-2xl max-w-md w-full p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3 mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-2 bg-red-500/10 rounded-lg\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_EnvelopeIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"h-5 w-5 text-red-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 552,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                            lineNumber: 551,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-white\",\n                                            children: \"Cancel Subscription\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                            lineNumber: 554,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                    lineNumber: 550,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-300 mb-6 text-sm\",\n                                    children: \"We're sorry to see you go! Please help us improve by telling us why you're cancelling.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                    lineNumber: 557,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-white mb-2\",\n                                                    children: \"Reason for cancellation *\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                    lineNumber: 563,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                    value: cancelReason,\n                                                    onChange: (e)=>setCancelReason(e.target.value),\n                                                    className: \"w-full p-3 border border-gray-600 rounded-lg bg-gray-800 text-white focus:ring-2 focus:ring-cyan-500 focus:border-cyan-500 text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"\",\n                                                            children: \"Select a reason...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                            lineNumber: 571,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        cancelReasons.map((reason)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: reason,\n                                                                children: reason\n                                                            }, reason, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                                lineNumber: 573,\n                                                                columnNumber: 23\n                                                            }, this))\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                    lineNumber: 566,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                            lineNumber: 562,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-white mb-2\",\n                                                    children: \"Additional feedback (optional)\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                    lineNumber: 579,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                    value: cancelFeedback,\n                                                    onChange: (e)=>setCancelFeedback(e.target.value),\n                                                    placeholder: \"Tell us more about your experience or what we could do better...\",\n                                                    rows: 3,\n                                                    className: \"w-full p-3 border border-gray-600 rounded-lg bg-gray-800 text-white placeholder-gray-400 focus:ring-2 focus:ring-cyan-500 focus:border-cyan-500 text-sm\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                    lineNumber: 582,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                            lineNumber: 578,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                    lineNumber: 561,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-3 mt-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"outline\",\n                                            onClick: ()=>setShowCancelModal(false),\n                                            className: \"flex-1 text-gray-300 border-gray-600 hover:bg-gray-800/50 text-sm\",\n                                            children: \"Keep Subscription\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                            lineNumber: 593,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            onClick: handleCancelSubscription,\n                                            disabled: loading || !cancelReason.trim(),\n                                            className: \"flex-1 bg-red-600 hover:bg-red-700 text-white text-sm\",\n                                            children: loading ? 'Cancelling...' : 'Cancel Subscription'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                            lineNumber: 600,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                    lineNumber: 592,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                            lineNumber: 549,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                        lineNumber: 548,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ConfirmationModal__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        isOpen: confirmation.isOpen,\n                        onClose: confirmation.hideConfirmation,\n                        onConfirm: confirmation.onConfirm,\n                        title: confirmation.title,\n                        message: confirmation.message,\n                        confirmText: confirmation.confirmText,\n                        cancelText: confirmation.cancelText,\n                        type: confirmation.type,\n                        isLoading: confirmation.isLoading\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                        lineNumber: 615,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                lineNumber: 312,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n        lineNumber: 291,\n        columnNumber: 5\n    }, this);\n}\n_s(BillingPage, \"g9QIw9Fa+MKpV9XVz4sDqaF1390=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams,\n        _hooks_useSubscription__WEBPACK_IMPORTED_MODULE_5__.useSubscription,\n        _hooks_useConfirmation__WEBPACK_IMPORTED_MODULE_8__.useConfirmation\n    ];\n});\n_c = BillingPage;\nvar _c;\n$RefreshReg$(_c, \"BillingPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/billing/page.tsx\n"));

/***/ })

});